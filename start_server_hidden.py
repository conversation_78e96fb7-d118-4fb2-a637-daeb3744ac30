"""
无窗口启动服务器脚本
避免控制台交互问题
"""
import subprocess
import sys
import os

def start_server_hidden():
    """以无窗口模式启动服务器"""
    try:
        # 获取当前脚本目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        app_path = os.path.join(current_dir, "app.py")
        
        # 检查 app.py 是否存在
        if not os.path.exists(app_path):
            print(f"错误：找不到 {app_path}")
            return False
        
        print("正在以后台模式启动服务器...")
        print("服务器将在后台运行，不会有控制台窗口")
        print("要停止服务器，请使用任务管理器结束 python.exe 进程")
        
        # 使用 pythonw 启动（无窗口模式）
        subprocess.Popen([
            sys.executable.replace("python.exe", "pythonw.exe"),
            app_path
        ], 
        creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0,
        stdout=subprocess.DEVNULL,
        stderr=subprocess.DEVNULL
        )
        
        print("✓ 服务器已在后台启动")
        print("✓ 服务地址: http://localhost:5000")
        return True
        
    except Exception as e:
        print(f"✗ 启动服务器失败: {e}")
        return False

if __name__ == "__main__":
    start_server_hidden()
    input("按 Enter 键退出...")
