"""
Windows 控制台 QuickEdit 模式禁用脚本
解决点击控制台导致服务器暂停的问题
"""
import ctypes
from ctypes import wintypes
import sys

def disable_quickedit():
    """禁用 Windows 控制台的 QuickEdit 模式"""
    try:
        # 获取控制台句柄
        kernel32 = ctypes.windll.kernel32
        handle = kernel32.GetStdHandle(-10)  # STD_INPUT_HANDLE
        
        # 获取当前控制台模式
        mode = wintypes.DWORD()
        kernel32.GetConsoleMode(handle, ctypes.byref(mode))
        
        # 禁用 QuickEdit 模式 (ENABLE_QUICK_EDIT_MODE = 0x0040)
        # 禁用 Insert 模式 (ENABLE_INSERT_MODE = 0x0020)
        new_mode = mode.value & ~(0x0040 | 0x0020)
        
        # 设置新的控制台模式
        result = kernel32.SetConsoleMode(handle, new_mode)
        
        if result:
            print("✓ 已成功禁用控制台 QuickEdit 模式")
            print("✓ 现在点击控制台不会导致服务器暂停")
            return True
        else:
            print("✗ 禁用 QuickEdit 模式失败")
            return False
            
    except Exception as e:
        print(f"✗ 禁用 QuickEdit 模式时出错: {e}")
        return False

if __name__ == "__main__":
    print("正在禁用 Windows 控制台 QuickEdit 模式...")
    disable_quickedit()
