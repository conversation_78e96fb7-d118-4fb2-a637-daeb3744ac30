# 生产环境部署建议

## 问题说明
Windows 控制台的 QuickEdit 模式会在用户点击控制台文字时暂停整个进程，导致 Web 服务无法响应。

## 解决方案

### 1. 立即恢复（紧急情况）
- 按 **ESC** 键退出选择模式
- 或按 **Enter** 键
- 或右键点击控制台选择"取消选择"

### 2. 代码级解决方案
已在 `app.py` 中集成了 QuickEdit 禁用功能，启动时会自动禁用。

### 3. 启动脚本
使用提供的启动脚本：
- `start_server.bat` - 带提示的控制台启动
- `start_server_hidden.py` - 无窗口后台启动

### 4. 生产环境最佳实践

#### 选项A：Windows 服务
```bash
# 安装为 Windows 服务（推荐）
pip install pywin32
python -m pip install python-windows-service
```

#### 选项B：使用 PM2
```bash
# 安装 PM2
npm install -g pm2
# 启动应用
pm2 start app.py --interpreter python --name "flask-app"
```

#### 选项C：使用 Docker
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5000
CMD ["python", "app.py"]
```

#### 选项D：使用 IIS + FastCGI
配置 IIS 与 Python FastCGI 模块

### 5. 监控和日志
- 使用日志文件而非控制台输出
- 配置日志轮转
- 设置监控告警

### 6. 安全建议
- 不要在生产环境使用控制台运行
- 配置防火墙规则
- 使用 HTTPS
- 定期更新依赖包

## 当前状态检查
运行以下命令检查服务器状态：
```bash
# 检查进程
tasklist | findstr python

# 检查端口
netstat -an | findstr :5000

# 测试连接
curl http://localhost:5000/captcha
```
